#!/usr/bin/env node

/**
 * Production Readiness Validation Script
 * Comprehensive validation for the optimized Xcode MCP Server
 */

import fs from "fs";
import path from "path";
import { execSync } from "child_process";

console.log("🔍 Validating Production-Ready Xcode MCP Server...\n");

// Test 1: Check if all consolidated tool files exist
const consolidatedTools = [
  "src/tools/project-tools.ts",
  "src/tools/file-tools.ts",
  "src/tools/build-tools.ts",
  "src/tools/development-tools.ts",
  "src/tools/package-tools.ts",
  "src/tools/simulator-tools.ts",
  "src/tools/xcode-tools.ts",
];

console.log("📁 Checking consolidated tool files:");
let allToolsExist = true;
for (const toolFile of consolidatedTools) {
  const exists = fs.existsSync(toolFile);
  console.log(`  ${exists ? "✅" : "❌"} ${toolFile}`);
  if (!exists) allToolsExist = false;
}

// Test 2: Check if all service files exist
const serviceFiles = [
  "src/services/service-container.ts",
  "src/services/path-service.ts",
  "src/services/command-service.ts",
  "src/services/cache-service.ts",
  "src/services/file-service.ts",
];

console.log("\n🔧 Checking service files:");
let allServicesExist = true;
for (const serviceFile of serviceFiles) {
  const exists = fs.existsSync(serviceFile);
  console.log(`  ${exists ? "✅" : "❌"} ${serviceFile}`);
  if (!exists) allServicesExist = false;
}

// Test 3: Check if base tool classes exist
const baseFiles = ["src/tools/base/tool-base.ts"];

console.log("\n🏗️  Checking base tool classes:");
let allBaseFilesExist = true;
for (const baseFile of baseFiles) {
  const exists = fs.existsSync(baseFile);
  console.log(`  ${exists ? "✅" : "❌"} ${baseFile}`);
  if (!exists) allBaseFilesExist = false;
}

// Test 4: Check if obsolete files have been removed
const obsoleteFiles = [
  "src/tools/core/fileOperations.ts",
  "src/tools/core/buildSystem.ts",
  "src/tools/project/index.ts",
  "scripts/validate.js",
  "assets/path-resolution-workflow.md",
  "docs/API.md",
  "docs/ARCHITECTURE.md",
];

console.log("\n🗑️  Checking obsolete files removed:");
let allObsoleteRemoved = true;
for (const obsoleteFile of obsoleteFiles) {
  const exists = fs.existsSync(obsoleteFile);
  console.log(
    `  ${!exists ? "✅" : "❌"} ${obsoleteFile} ${
      !exists ? "(removed)" : "(still exists)"
    }`
  );
  if (exists) allObsoleteRemoved = false;
}

// Test 5: Check if enhanced CLI is in place
console.log("\n🎨 Checking enhanced CLI interface:");
const indexContent = fs.readFileSync("src/index.ts", "utf-8");
const hasEnhancedBanner = indexContent.includes(
  "╭─────────────────────────────────────────────────────────────╮"
);
const hasProgressBars = indexContent.includes("displayInitProgress");
const hasStructuredStatus = indexContent.includes("Server Status");

console.log(`  ${hasEnhancedBanner ? "✅" : "❌"} Enhanced ASCII banner`);
console.log(`  ${hasProgressBars ? "✅" : "❌"} Progress visualization`);
console.log(
  `  ${hasStructuredStatus ? "✅" : "❌"} Structured status reporting`
);

// Test 6: Count tool registrations in consolidated files
console.log("\n📊 Counting consolidated tools:");
let totalTools = 0;

for (const toolFile of consolidatedTools) {
  if (fs.existsSync(toolFile)) {
    const content = fs.readFileSync(toolFile, "utf-8");
    const toolMatches = content.match(/server\.server\.tool\(/g);
    const toolCount = toolMatches ? toolMatches.length : 0;
    console.log(`  ${path.basename(toolFile)}: ${toolCount} tools`);
    totalTools += toolCount;
  }
}

console.log(`  📈 Total consolidated tools: ${totalTools}`);

// Test 7: Check build status
console.log("\n🔨 Checking build status:");
const distExists = fs.existsSync("dist");
const distIndexExists = fs.existsSync("dist/index.js");
console.log(`  ${distExists ? "✅" : "❌"} dist directory exists`);
console.log(`  ${distIndexExists ? "✅" : "❌"} dist/index.js exists`);

// Final summary
console.log("\n" + "=".repeat(60));
console.log("📋 CONSOLIDATION VALIDATION SUMMARY");
console.log("=".repeat(60));

const checks = [
  { name: "Consolidated tool files", passed: allToolsExist },
  { name: "Service files", passed: allServicesExist },
  { name: "Base tool classes", passed: allBaseFilesExist },
  { name: "Obsolete files removed", passed: allObsoleteRemoved },
  {
    name: "Enhanced CLI interface",
    passed: hasEnhancedBanner && hasProgressBars && hasStructuredStatus,
  },
  { name: "Build artifacts", passed: distExists && distIndexExists },
];

let passedChecks = 0;
for (const check of checks) {
  console.log(`${check.passed ? "✅" : "❌"} ${check.name}`);
  if (check.passed) passedChecks++;
}

const successRate = Math.round((passedChecks / checks.length) * 100);
console.log(
  `\n🎯 Success Rate: ${successRate}% (${passedChecks}/${checks.length} checks passed)`
);

if (successRate === 100) {
  console.log("\n🎉 CONSOLIDATION VALIDATION PASSED!");
  console.log(
    "✨ The Xcode MCP Server has been successfully consolidated and optimized."
  );
} else {
  console.log(
    "\n⚠️  Some validation checks failed. Please review the issues above."
  );
}

console.log(`\n📈 Progress: ${totalTools} tools consolidated so far`);
console.log("🚀 Ready for Phase 5: Final validation and testing");
