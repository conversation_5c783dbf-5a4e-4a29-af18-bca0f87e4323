/**
 * Consolidated Tool Base Classes
 * Merges and enhances functionality from toolInfrastructure.ts
 */

import { z } from "zod";
import { XcodeServer } from "../../server.js";
import {
  XcodeServerError,
  ValidationError,
  ProjectNotFoundError,
  PathAccessError,
  FileOperationError,
} from "../../utils/core/errors.js";

// Import MCP types for proper tool result typing
type ToolResult = {
  content: Array<{
    type: "text";
    text: string;
  }>;
  isError?: boolean;
};

/**
 * Tool handler function type
 */
export type ToolHandler<T = any> = (
  params: T,
  server: XcodeServer
) => Promise<ToolResult>;

/**
 * Tool metadata interface
 */
export interface ToolMetadata {
  name: string;
  description: string;
  category: string;
  tags: string[];
  requiresActiveProject: boolean;
  requiresXcode: boolean;
  platforms: string[];
  version: string;
}

/**
 * Base class for all tool implementations
 */
export abstract class ToolBase<TParams = any> {
  protected server: XcodeServer;
  protected toolName: string;
  protected description: string;
  protected schema: z.ZodSchema<TParams>;
  protected metadata: Partial<ToolMetadata>;

  constructor(
    server: XcodeServer,
    toolName: string,
    description: string,
    schema: z.ZodSchema<TParams>,
    metadata: Partial<ToolMetadata> = {}
  ) {
    this.server = server;
    this.toolName = toolName;
    this.description = description;
    this.schema = schema;
    this.metadata = {
      name: toolName,
      description,
      category: "general",
      tags: [],
      requiresActiveProject: false,
      requiresXcode: false,
      platforms: ["ios", "macos"],
      version: "1.0.0",
      ...metadata,
    };
  }

  /**
   * Abstract method that must be implemented by subclasses
   */
  protected abstract executeImpl(params: TParams): Promise<ToolResult>;

  /**
   * Execute the tool with comprehensive error handling and validation
   */
  async execute(params: any): Promise<ToolResult> {
    const startTime = Date.now();

    try {
      // Validate parameters
      const validatedParams = this.validateParams(params);

      // Pre-execution checks
      await this.preExecutionChecks(validatedParams);

      // Execute the tool
      const result = await this.executeImpl(validatedParams);

      // Post-execution processing
      const processedResult = await this.postExecutionProcessing(result);

      // Log successful execution
      this.logExecution(true, Date.now() - startTime);

      return processedResult;
    } catch (error) {
      // Log failed execution
      this.logExecution(false, Date.now() - startTime, error);

      // Handle and format error
      return this.handleError(error);
    }
  }

  /**
   * Register the tool with the server
   */
  register(): void {
    this.server.server.tool(
      this.toolName,
      this.description,
      (this.schema as any).shape || {},
      async (params: any) => this.execute(params)
    );
  }

  /**
   * Validate input parameters
   */
  protected validateParams(params: any): TParams {
    try {
      return this.schema.parse(params);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const issues = error.issues
          .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
          .join(", ");
        throw new ValidationError(
          `Parameter validation failed: ${issues}`,
          params
        );
      }
      throw new ValidationError(
        `Parameter validation failed: ${String(error)}`,
        params
      );
    }
  }

  /**
   * Pre-execution checks (can be overridden by subclasses)
   */
  protected async preExecutionChecks(params: TParams): Promise<void> {
    // Check if active project is required
    if (this.metadata.requiresActiveProject && !this.server.activeProject) {
      throw new ProjectNotFoundError("This tool requires an active project");
    }

    // Check if Xcode is required
    if (this.metadata.requiresXcode) {
      await this.checkXcodeAvailability();
    }
  }

  /**
   * Post-execution processing (can be overridden by subclasses)
   */
  protected async postExecutionProcessing(
    result: ToolResult
  ): Promise<ToolResult> {
    return result;
  }

  /**
   * Handle errors and format them appropriately
   */
  protected handleError(error: any): ToolResult {
    let errorMessage: string;
    let isKnownError = false;

    if (error instanceof XcodeServerError) {
      errorMessage = error.message;
      isKnownError = true;
    } else if (error instanceof Error) {
      errorMessage = error.message;
    } else {
      errorMessage = String(error);
    }

    // Sanitize error message for security
    const sanitizedMessage = this.sanitizeErrorMessage(errorMessage);

    return {
      content: [
        {
          type: "text",
          text: `Error in ${this.toolName}: ${sanitizedMessage}`,
        },
      ],
      isError: true,
    };
  }

  /**
   * Log tool execution for monitoring
   */
  protected logExecution(
    success: boolean,
    executionTime: number,
    error?: any
  ): void {
    const logData = {
      tool: this.toolName,
      success,
      executionTime,
      timestamp: new Date().toISOString(),
      error: error ? String(error) : undefined,
    };

    if (process.env.DEBUG === "true") {
      console.error(
        `[${this.toolName}] ${
          success ? "SUCCESS" : "FAILED"
        } (${executionTime}ms)`
      );
      if (error) {
        console.error(`[${this.toolName}] Error:`, error);
      }
    }

    // TODO: Send to performance monitor
  }

  /**
   * Check Xcode availability
   */
  protected async checkXcodeAvailability(): Promise<void> {
    try {
      const { CommandService } = await import(
        "../../services/command-service.js"
      );
      await CommandService.execute("xcode-select", ["-p"], { timeout: 5000 });
    } catch (error) {
      throw new XcodeServerError(
        "Xcode is not available or not properly configured. Please install Xcode and run 'xcode-select --install'."
      );
    }
  }

  /**
   * Sanitize error messages to prevent information leakage
   */
  protected sanitizeErrorMessage(message: string): string {
    // Remove absolute paths
    let sanitized = message.replace(
      /\/[A-Za-z0-9_\-\.\/]+/g,
      "[PATH_REDACTED]"
    );

    // Remove user directories
    sanitized = sanitized.replace(/\/Users\/<USER>\/\s]+/g, "/Users/<USER>");

    // Remove environment variables
    sanitized = sanitized.replace(/\$[A-Z_][A-Z0-9_]*/g, "$[ENV_VAR]");

    return sanitized;
  }

  /**
   * Create a success result
   */
  protected createSuccessResult(text: string): ToolResult {
    return {
      content: [
        {
          type: "text",
          text,
        },
      ],
    };
  }

  /**
   * Create a resource result (simplified to text for MCP compatibility)
   */
  protected createResourceResult(
    uri: string,
    text?: string,
    mimeType?: string
  ): ToolResult {
    return {
      content: [
        {
          type: "text",
          text: text || `Resource: ${uri}${mimeType ? ` (${mimeType})` : ""}`,
        },
      ],
    };
  }
}

/**
 * Base class for file operation tools
 */
export abstract class FileToolBase<TParams = any> extends ToolBase<TParams> {
  constructor(
    server: XcodeServer,
    toolName: string,
    description: string,
    schema: z.ZodSchema<TParams>,
    metadata: Partial<ToolMetadata> = {}
  ) {
    super(server, toolName, description, schema, {
      category: "file",
      tags: ["file", "filesystem"],
      ...metadata,
    });
  }

  /**
   * Check if a file exists
   */
  protected async fileExists(filePath: string): Promise<boolean> {
    const { FileService } = await import("../../services/file-service.js");
    const fileService = new FileService();
    return fileService.exists(filePath);
  }

  /**
   * Get file stats with error handling
   */
  protected async getFileStats(filePath: string) {
    const { FileService } = await import("../../services/file-service.js");
    const fileService = new FileService();
    return fileService.getStats(filePath);
  }

  /**
   * Read file content safely
   */
  protected async readFileContent(
    filePath: string,
    options: { encoding?: BufferEncoding; asBinary?: boolean } = {}
  ): Promise<string | Buffer> {
    const { FileService } = await import("../../services/file-service.js");
    const fileService = new FileService();
    return fileService.readFile(filePath, options);
  }

  /**
   * Write file content safely
   */
  protected async writeFileContent(
    filePath: string,
    content: string | Buffer,
    options: { encoding?: BufferEncoding; createPath?: boolean } = {}
  ): Promise<void> {
    const { FileService } = await import("../../services/file-service.js");
    const fileService = new FileService();
    return fileService.writeFile(filePath, content, options);
  }
}

/**
 * Base class for project operation tools
 */
export abstract class ProjectToolBase<TParams = any> extends ToolBase<TParams> {
  constructor(
    server: XcodeServer,
    toolName: string,
    description: string,
    schema: z.ZodSchema<TParams>,
    metadata: Partial<ToolMetadata> = {}
  ) {
    super(server, toolName, description, schema, {
      category: "project",
      tags: ["project", "xcode"],
      requiresActiveProject: true,
      requiresXcode: true,
      ...metadata,
    });
  }

  /**
   * Get the active project path
   */
  protected getActiveProjectPath(): string {
    if (!this.server.activeProject) {
      throw new ProjectNotFoundError("No active project available");
    }
    return this.server.activeProject.path;
  }

  /**
   * Get the active project directory
   */
  protected getActiveProjectDirectory(): string {
    const projectPath = this.getActiveProjectPath();
    const path = require("path");
    return path.dirname(projectPath);
  }
}

/**
 * Base class for command-line tool wrappers
 */
export abstract class CommandToolBase<TParams = any> extends ToolBase<TParams> {
  protected abstract getCommand(): string;
  protected abstract buildArgs(params: TParams): string[];

  constructor(
    server: XcodeServer,
    toolName: string,
    description: string,
    schema: z.ZodSchema<TParams>,
    metadata: Partial<ToolMetadata> = {}
  ) {
    super(server, toolName, description, schema, {
      category: "command",
      tags: ["command", "cli"],
      requiresXcode: true,
      ...metadata,
    });
  }

  /**
   * Execute the command with standardized handling
   */
  protected async executeToolCommand(
    params: TParams,
    options: { timeout?: number; cwd?: string } = {}
  ): Promise<{ stdout: string; stderr: string }> {
    const command = this.getCommand();
    const args = this.buildArgs(params);

    const { CommandService } = await import(
      "../../services/command-service.js"
    );
    const result = await CommandService.execute(command, args, options);

    return {
      stdout: result.stdout,
      stderr: result.stderr,
    };
  }
}

/**
 * Tool factory for registering tools
 */
export class ToolFactory {
  private static tools = new Map<string, ToolBase>();

  /**
   * Register a tool
   */
  static register<T>(
    name: string,
    toolClass: new (server: XcodeServer) => ToolBase<T>
  ): void {
    // This will be implemented when we migrate tools to use classes
  }

  /**
   * Get registered tool
   */
  static getTool(name: string): ToolBase | undefined {
    return this.tools.get(name);
  }

  /**
   * Get all registered tools
   */
  static getAllTools(): Map<string, ToolBase> {
    return new Map(this.tools);
  }
}
